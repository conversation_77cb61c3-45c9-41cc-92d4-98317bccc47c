1. 進貨功能：透過 `purchase_orders` 和 `purchase_order_items` 表記錄採購單據和明細，進貨時會在 `inventory_transactions` 中記錄庫存增加。
2. 銷售消耗：當進行銷售時，會在 `inventory_transactions` 中記錄庫存減少。
3. 退貨功能：`returns` 和 `return_items` 表處理向供應商退貨和客戶退貨的情況，並在 `inventory_transactions` 中記錄對應的庫存變動。
4. 庫存盤點：`inventory_counts` 和 `inventory_count_items` 表處理定期盤點，記錄系統庫存和實際庫存的差異。
5. 批次管理：`product_batches` 表支持批次追蹤，適用於需要跟蹤有效期或批號的產品。
6. 庫存報警：`inventory_alerts` 表記錄庫存異常情況，如低於最低庫存量的產品。