// 版本檢查服務 - 專注於版本檢測邏輯，只在路由切換或組件渲染時檢查
export class VersionChecker {
  private static instance: VersionChecker;
  private currentVersion: string;
  private buildVersion: string;

  private constructor() {
    // 從環境變數獲取構建版本（包含時間戳）
    this.buildVersion = process.env.APP_VERSION || 'unknown';
    // 提取基礎版本號（去掉時間戳）
    this.currentVersion = this.extractBaseVersion(this.buildVersion);
  }

  private extractBaseVersion(fullVersion: string): string {
    // 從 "v1.2.4_123456" 格式中提取 "v1.2.4"
    const match = fullVersion.match(/^(v\d+\.\d+\.\d+)/);
    return match ? match[1] : fullVersion;
  }

  public static getInstance(): VersionChecker {
    if (!VersionChecker.instance) {
      VersionChecker.instance = new VersionChecker();
    }
    return VersionChecker.instance;
  }

  // 初始化版本檢查器，設置初始值
  public async initialize(): Promise<void> {
    // 在開發環境中跳過初始化
    if (process.env.DEV) {
      return;
    }

    try {
      // 初始化 index.html 的 ETag 和 Last-Modified
      await this.initializeIndexCache();

      // 初始化版本信息
      await this.initializeVersionCache();
    } catch (error) {
      console.warn('VersionChecker: 初始化失敗，將在下次檢查時重試', error);
    }
  }

  private async initializeIndexCache(): Promise<void> {
    const storedEtag = localStorage.getItem('app-etag');
    const storedLastModified = localStorage.getItem('app-last-modified');

    // 如果已經有儲存的值，跳過初始化
    if (storedEtag || storedLastModified) {
      return;
    }

    try {
      const basePath = this.getBasePath();
      const indexUrl = `${basePath}index.html?${Date.now()}`;

      const response = await fetch(indexUrl, {
        method: 'HEAD',
        cache: 'no-cache'
      });

      if (response.ok) {
        const etag = response.headers.get('etag');
        const lastModified = response.headers.get('last-modified');

        if (etag) localStorage.setItem('app-etag', etag);
        if (lastModified) localStorage.setItem('app-last-modified', lastModified);
      }
    } catch (error) {
      // 初始化失敗不影響應用運行
      console.warn('VersionChecker: 無法初始化 index.html 緩存', error);
    }
  }

  private async initializeVersionCache(): Promise<void> {
    const storedVersion = localStorage.getItem('app-stored-version');

    // 如果已經有儲存的版本，跳過初始化
    if (storedVersion) {
      return;
    }

    try {
      const basePath = this.getBasePath();
      const versionUrl = `${basePath}version.json?${Date.now()}`;

      const response = await fetch(versionUrl, {
        cache: 'no-cache'
      });

      if (response.ok) {
        const versionData = await response.json();
        const serverVersion = versionData.version;
        localStorage.setItem('app-stored-version', serverVersion || this.buildVersion);
      } else {
        // 如果沒有 version.json，使用本地構建版本
        localStorage.setItem('app-stored-version', this.buildVersion);
      }
    } catch (error) {
      // 初始化失敗，使用本地構建版本
      localStorage.setItem('app-stored-version', this.buildVersion);
    }
  }



  // 執行更新（由App.vue調用）
  public async performUpdate(): Promise<void> {
    try {
      // 清除所有緩存
      await this.clearAllCaches();

      // 重新載入頁面
      window.location.reload();
    } catch (error) {
      console.error('VersionChecker: 更新失敗', error);
      throw error; // 讓調用方處理錯誤
    }
  }



  private async clearAllCaches() {
    try {
      // 清除 Service Worker 緩存
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }

      // 清除 localStorage 中的版本相關資訊
      localStorage.removeItem('app-etag');
      localStorage.removeItem('app-last-modified');
      localStorage.removeItem('app-stored-version');
      localStorage.removeItem('app-last-check-time');
    } catch (error) {
      console.error('VersionChecker: 清除緩存失敗', error);
    }
  }

  public destroy() {
    // 不再需要清理定時器，因為已經移除了定時檢查功能
  }

  public getCurrentVersion(): string {
    return this.currentVersion;
  }

  public getBuildVersion(): string {
    return this.buildVersion;
  }

  public getVersionInfo() {
    return {
      baseVersion: this.currentVersion,
      buildVersion: this.buildVersion,
      timestamp: this.extractTimestamp(this.buildVersion)
    };
  }

  private extractTimestamp(fullVersion: string): string {
    // 從 "v1.2.4_123456" 格式中提取時間戳
    const match = fullVersion.match(/_(\d+)$/);
    return match ? match[1] : '';
  }

  // 獲取應用的基礎路徑
  private getBasePath(): string {
    // 在開發環境中使用根路徑
    if (process.env.DEV) {
      return '/';
    }

    // 在生產環境中，動態獲取基礎路徑
    // 從當前頁面的路徑中提取基礎路徑
    const currentPath = window.location.pathname;

    // 如果當前路徑包含 /au-pos/，則使用 /au-pos/
    if (currentPath.includes('/au-pos/')) {
      return '/au-pos/';
    }

    // 否則嘗試從 router base 獲取
    const routerBase = process.env.VUE_ROUTER_BASE || '/';
    return routerBase.endsWith('/') ? routerBase : routerBase + '/';
  }

  // 公共更新方法（由App.vue調用）
  public async triggerUpdate(): Promise<void> {
    return this.performUpdate();
  }

  // 手動檢查版本
  public async manualVersionCheck(): Promise<boolean> {
    // 在開發環境中不執行手動版本檢查
    if (process.env.DEV) {
      console.log('VersionChecker: 開發環境，跳過版本檢查');
      return false;
    }

    // 檢查是否為第一次會話（避免無痕模式和首次訪問時的誤報）
    const isFirstSession = !sessionStorage.getItem('app-session-started');
    if (isFirstSession) {
      console.log('VersionChecker: 第一次會話，跳過版本檢查');
      return false;
    }

    // 添加防抖機制，避免短時間內重複檢查
    const lastCheckTime = localStorage.getItem('app-last-check-time');
    const now = Date.now();
    if (lastCheckTime && (now - parseInt(lastCheckTime)) < 5000) { // 5秒內不重複檢查
      console.log('VersionChecker: 短時間內已檢查過，跳過');
      return false;
    }
    localStorage.setItem('app-last-check-time', now.toString());

    try {
      console.log('VersionChecker: 開始版本檢查');

      // 檢查 index.html 的變更
      const indexChanged = await this.checkIndexChanges();
      console.log('VersionChecker: index.html 檢查結果:', indexChanged);
      if (indexChanged) {
        return true;
      }

      // 檢查版本文件
      const versionChanged = await this.checkVersionFileChanges();
      console.log('VersionChecker: version.json 檢查結果:', versionChanged);
      if (versionChanged) {
        return true;
      }

      console.log('VersionChecker: 沒有發現版本更新');
      return false;
    } catch (error) {
      console.error('VersionChecker: 手動版本檢查失敗', error);
      throw error;
    }
  }

  private async checkIndexChanges(): Promise<boolean> {
    // 獲取正確的基礎路徑
    const basePath = this.getBasePath();
    const indexUrl = `${basePath}index.html?${Date.now()}`;

    console.log('VersionChecker: 檢查 index.html:', indexUrl);

    const response = await fetch(indexUrl, {
      method: 'HEAD',
      cache: 'no-cache'
    });

    if (!response.ok) {
      throw new Error('無法獲取 index.html');
    }

    const etag = response.headers.get('etag');
    const lastModified = response.headers.get('last-modified');

    const storedEtag = localStorage.getItem('app-etag');
    const storedLastModified = localStorage.getItem('app-last-modified');

    console.log('VersionChecker: index.html 比較', {
      currentEtag: etag,
      storedEtag: storedEtag,
      currentLastModified: lastModified,
      storedLastModified: storedLastModified
    });

    // 如果沒有儲存的值，表示是第一次訪問，儲存當前值但不觸發更新
    if (!storedEtag && !storedLastModified) {
      console.log('VersionChecker: 第一次檢查 index.html，儲存初始值');
      if (etag) localStorage.setItem('app-etag', etag);
      if (lastModified) localStorage.setItem('app-last-modified', lastModified);
      return false;
    }

    // 檢查是否有變化
    let hasChanged = false;

    if (etag && storedEtag && etag !== storedEtag) {
      console.log('VersionChecker: ETag 已變更');
      hasChanged = true;
    } else if (lastModified && storedLastModified && lastModified !== storedLastModified) {
      console.log('VersionChecker: Last-Modified 已變更');
      hasChanged = true;
    }

    // 如果有變化，更新儲存的值
    if (hasChanged) {
      if (etag) localStorage.setItem('app-etag', etag);
      if (lastModified) localStorage.setItem('app-last-modified', lastModified);
    }

    return hasChanged;
  }

  private async checkVersionFileChanges(): Promise<boolean> {
    // 在開發環境中跳過版本文件檢查
    if (process.env.DEV) {
      return false;
    }

    try {
      // 獲取正確的基礎路徑
      const basePath = this.getBasePath();
      const versionUrl = `${basePath}version.json?${Date.now()}`;

      const response = await fetch(versionUrl, {
        cache: 'no-cache'
      });

      if (response.ok) {
        const versionData = await response.json();
        const serverVersion = versionData.version;

        // 檢查是否有儲存的版本信息，如果沒有表示是第一次訪問
        const storedVersion = localStorage.getItem('app-stored-version');
        if (!storedVersion) {
          // 儲存當前版本供下次比較，使用服務器版本而不是本地構建版本
          localStorage.setItem('app-stored-version', serverVersion || this.buildVersion);
          return false;
        }

        // 比較服務器版本與儲存的版本
        if (serverVersion && serverVersion !== storedVersion) {
          // 更新儲存的版本
          localStorage.setItem('app-stored-version', serverVersion);
          return true;
        }
      }
      return false;
    } catch (error) {
      // 版本文件不存在或無法訪問(不存在是正常的)，不應該觸發更新
      return false;
    }
  }
}

// 創建全局實例
export const versionChecker = VersionChecker.getInstance();
