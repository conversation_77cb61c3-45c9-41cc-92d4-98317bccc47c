# Xero API Scopes 權限指南

## 問題回答

**Q: `accounting.transactions accounting.contacts` 是否足夠取得 invoices？**

**A: ✅ 是的，完全足夠！**

## 詳細分析

### 當前設置的 Scopes

您目前的設置：
```
accounting.transactions accounting.contacts
```

### `accounting.transactions` 權限詳情

這個 scope 提供對所有會計交易的完整訪問權限，包括：

#### ✅ 發票相關 (Invoices)
- `GET /Invoices` - 讀取發票列表
- `GET /Invoices/{InvoiceID}` - 讀取單一發票
- `POST /Invoices` - 創建發票
- `PUT /Invoices/{InvoiceID}` - 更新發票
- `DELETE /Invoices/{InvoiceID}` - 刪除發票

#### ✅ 其他交易類型
- Bills (帳單)
- Bank Transactions (銀行交易)
- Credit Notes (信用票據)
- Payments (付款)
- Prepayments (預付款)
- Overpayments (超額付款)

### `accounting.contacts` 權限詳情

這個 scope 提供聯絡人管理權限：
- `GET /Contacts` - 讀取聯絡人列表
- `GET /Contacts/{ContactID}` - 讀取單一聯絡人
- `POST /Contacts` - 創建聯絡人
- `PUT /Contacts/{ContactID}` - 更新聯絡人

## 權限充足性驗證

### ✅ 取得發票列表
```bash
# 您的應用可以成功調用
GET https://api.xero.com/api.xro/2.0/Invoices
```

### ✅ 取得單一發票
```bash
# 您的應用可以成功調用
GET https://api.xero.com/api.xro/2.0/Invoices/{InvoiceID}
```

### ✅ 創建發票
```bash
# 您的應用可以成功調用
POST https://api.xero.com/api.xro/2.0/Invoices
```

## 可選但建議的額外 Scopes

雖然當前設置已經足夠，但建議考慮添加以下 scope：

### `accounting.settings` (建議添加)
**用途**: 讀取組織基礎設定
**提供的功能**:
- 稅率設定 (Tax Rates)
- 科目表 (Chart of Accounts)
- 組織資訊 (Organisation)
- 貨幣設定 (Currencies)
- 追蹤類別 (Tracking Categories)

**為什麼建議**:
- 創建發票時需要正確的稅率代碼
- 需要有效的科目代碼
- 確保貨幣設定正確

### 完整建議設置
```
accounting.transactions accounting.contacts accounting.settings
```

## 實際測試

### 測試當前設置
您可以使用我們提供的測試腳本來驗證：

```bash
# 測試發票 API
go run test_xero_fix.go YOUR_ACCESS_TOKEN
```

### 前端驗證
在 Xero 設置頁面中，您會看到：
- ✅ accounting.transactions - 可以讀取和管理發票、帳單等交易
- ✅ accounting.contacts - 可以讀取和管理聯絡人
- ⓘ 建議添加 accounting.settings - 用於讀取稅率和組織設定

## 常見問題

### Q1: 為什麼有時候會收到 403 錯誤？
**A**: 403 錯誤通常不是因為 scope 不足，而是因為：
1. Token 已過期或被撤銷
2. Tenant ID 不匹配
3. 應用未正確連接到組織

### Q2: 需要 `accounting.settings` 嗎？
**A**: 對於基本的發票讀取，不是必需的。但如果您要：
- 創建發票
- 需要稅率信息
- 需要科目代碼
- 需要組織詳細信息

那麼建議添加此 scope。

### Q3: 如何驗證 scope 是否正確？
**A**: 
1. 使用我們的診斷工具
2. 檢查 Xero 開發者控制台中的應用權限
3. 運行測試腳本

## 結論

**您當前的 `accounting.transactions accounting.contacts` 設置完全足夠取得 invoices。**

如果您遇到 403 錯誤，問題很可能是：
1. Token 過期 - 使用我們的自動刷新機制
2. 連接問題 - 使用診斷工具檢查
3. 配置問題 - 檢查 Client ID、Secret 和 Tenant ID

建議按照我們提供的解決方案進行故障排除，而不是修改 scopes。
